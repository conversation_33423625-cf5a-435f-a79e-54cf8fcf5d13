using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.BaseForCalculationBasePayrollComponent.Constants;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Models;
using Vsp.PayrollConfiguration.Domain.Shared.Models;
using Vsp.PayrollConfiguration.Infrastructure.Models;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.BaseForCalculationBasePayrollComponent;

[Collection(EntityNames.BaseForCalculationBasePayrollComponent)]
public class PostBaseForCalculationBasePayrollComponentByBaseForCalculationIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "BaseForCalculationBasePayrollComponent";
    protected override bool UseTransaction => true;

    // Using the same base for calculation ID that works in the GET available components test
    // private static readonly Guid QA_BFC_BasePayrollComponent_BFC1_POST_CLA = Guid.Parse("000009d1-07e9-0001-0100-000000000000");
    // private static readonly Guid QA_BFC_BasePayrollComponent_BFC1_POST_WM = Guid.Parse("000009d1-07e9-0001-0100-000000000000"); // Using same for now
    // private static readonly Guid QA_BFC_BasePayrollComponent_BFC1_POST_PA = Guid.Parse("000009d1-07e9-0001-0100-000000000000"); // Using same for now
    private static readonly Guid QA_BFC_BasePayrollComponent_BFC1_POST_CLA = Guid.Parse("000009f4-07e9-0001-0100-000000000000"); // InheritanceLevel 2548, year 2025, BaseForCalculation 1
    private static readonly Guid QA_BFC_BasePayrollComponent_BFC1_POST_WM = Guid.Parse("000009f6-07e9-0001-0100-000000000000"); // InheritanceLevel 2550, year 2025, BaseForCalculation 1
    private static readonly Guid QA_BFC_BasePayrollComponent_BFC1_POST_PA = Guid.Parse("000009fa-07e9-0001-0100-000000000000"); // InheritanceLevel 2554, year 2025, BaseForCalculation 1
    
    #region OK

    [Fact]
    public async Task Ok_QA_BaseForCalculationBasePayrollComponent_POST_CLA() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationBasePayrollComponentRoutes.PostBaseForCalculationBasePayrollComponentByBaseForCalculationIdAsync.Replace("{baseForCalculationId:guid}", QA_BFC_BasePayrollComponent_BFC1_POST_CLA.ToString())}",
                Method = HttpMethod.Post,
                Body = GetBaseForCalculationBasePayrollComponentPostModel(model =>
                {
                  
                    model.Year = 2025;
                    model.PayrollComponent = new KeyModel { Key = 257 };
                    model.StartPayrollPeriod = new PayrollPeriodNumberModel { PeriodNumber = 1 };
                })
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.Created,
                BodyValidation = JsonBodyValidation.Default,
            });

    [Fact]
    public async Task Ok_QA_BaseForCalculationBasePayrollComponent_POST_WM() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationBasePayrollComponentRoutes.PostBaseForCalculationBasePayrollComponentByBaseForCalculationIdAsync.Replace("{baseForCalculationId:guid}", QA_BFC_BasePayrollComponent_BFC1_POST_WM.ToString())}",
                Method = HttpMethod.Post,
                Body = GetBaseForCalculationBasePayrollComponentPostModel(model =>
                {
                    model.Year = 2025;
                    model.PayrollComponent = new KeyModel { Key = 257 };
                    model.StartPayrollPeriod = new PayrollPeriodNumberModel { PeriodNumber = 1 };
                })
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.Created,
                BodyValidation = JsonBodyValidation.Default,
            });

    [Fact]
    public async Task Ok_QA_BaseForCalculationBasePayrollComponent_POST_PA() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationBasePayrollComponentRoutes.PostBaseForCalculationBasePayrollComponentByBaseForCalculationIdAsync.Replace("{baseForCalculationId:guid}", QA_BFC_BasePayrollComponent_BFC1_POST_PA.ToString())}",
                Method = HttpMethod.Post,
                Body = GetBaseForCalculationBasePayrollComponentPostModel(model =>
                {
                    model.Year = 2025;
                    model.PayrollComponent = new KeyModel { Key = 257 };
                    model.StartPayrollPeriod = new PayrollPeriodNumberModel { PeriodNumber = 1 };
                })
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.Created,
                BodyValidation = JsonBodyValidation.Default,
            });

    #endregion

    private BaseForCalculationBasePayrollComponentPostModel GetBaseForCalculationBasePayrollComponentPostModel(Action<BaseForCalculationBasePayrollComponentPostModel>? setNewPropertyValues = null)
    {
        var postModel = new BaseForCalculationBasePayrollComponentPostModel
        {
            Year = 2025,
            PayrollComponent = new KeyModel { Key = 257 },
            StartPayrollPeriod = new PayrollPeriodNumberModel { PeriodNumber = 1 },
            Origin = new KeyValueModel { Key = 2 }
        };

        setNewPropertyValues?.Invoke(postModel);
        return postModel;
    }
}